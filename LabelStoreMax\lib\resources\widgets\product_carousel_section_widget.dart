import 'package:flutter/material.dart';
import 'package:carousel_slider/carousel_slider.dart';
import '/app/models/woocommerce_wrappers/my_product.dart';
import '/resources/widgets/cached_image_widget.dart';
import '/resources/themes/styles/design_constants.dart';

import '/bootstrap/helpers.dart';

/// Reusable Product Carousel Section Widget
/// Eliminates code duplication across different product sections
class ProductCarouselSectionWidget extends StatelessWidget {
  final String title;
  final Future<List<MyProduct>> productsFuture;
  final Function(MyProduct) onProductTap;
  final List<MyProduct> Function(List<MyProduct>)? productFilter;
  final String emptyMessage;
  final String errorMessage;
  final int maxProducts;
  final Duration autoPlayInterval;
  final double viewportFraction;
  final double enlargeFactor;
  final bool enlargeCenterPage;

  const ProductCarouselSectionWidget({
    Key? key,
    required this.title,
    required this.productsFuture,
    required this.onProductTap,
    this.productFilter,
    this.emptyMessage = 'لا توجد منتجات متاحة',
    this.errorMessage = 'خطأ في تحميل المنتجات',
    this.maxProducts = 10,
    this.autoPlayInterval = const Duration(seconds: 4),
    this.viewportFraction = 0.7,
    this.enlargeFactor = 0.2,
    this.enlargeCenterPage = true,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(vertical: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Section Header
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 16),
            child: Text(
              title,
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: Theme.of(context).colorScheme.onSurface,
              ),
            ),
          ),
          SizedBox(height: DesignConstants.compactSpacing),
          
          // Product Carousel
          SizedBox(
            height: DesignConstants.getResponsiveHeight(context,
              mobileRatio: 0.35,    // 35% of screen height on mobile
              tabletRatio: 0.30,    // 30% of screen height on tablet
              desktopRatio: 0.25,   // 25% of screen height on desktop
            ),
            child: FutureBuilder<List<MyProduct>>(
              future: productsFuture,
              builder: (context, snapshot) {
                if (snapshot.connectionState == ConnectionState.waiting) {
                  return Center(child: CircularProgressIndicator());
                }

                if (snapshot.hasError) {
                  return Center(
                    child: Text(
                      errorMessage,
                      style: Theme.of(context).textTheme.bodyMedium,
                    ),
                  );
                }

                List<MyProduct> products = snapshot.data ?? [];
                
                // Apply filter if provided
                if (productFilter != null) {
                  products = productFilter!(products);
                }
                
                products = products.take(maxProducts).toList();

                if (products.isEmpty) {
                  return Center(
                    child: Text(
                      emptyMessage,
                      style: Theme.of(context).textTheme.bodyMedium,
                    ),
                  );
                }

                // Create flat list of all images from products
                List<Map<String, dynamic>> allImages = [];
                for (MyProduct product in products) {
                  if (product.hasImages()) {
                    for (var image in product.images) {
                      allImages.add({
                        'product': product,
                        'imageUrl': image.getSafeImageSrc(),
                      });
                    }
                  } else {
                    allImages.add({
                      'product': product,
                      'imageUrl': '',
                    });
                  }
                }

                return CarouselSlider.builder(
                  itemCount: allImages.length,
                  options: CarouselOptions(
                    height: DesignConstants.getResponsiveHeight(context,
                      mobileRatio: 0.35,
                      tabletRatio: 0.30,
                      desktopRatio: 0.25,
                    ),
                    autoPlay: true,
                    autoPlayInterval: autoPlayInterval,
                    autoPlayAnimationDuration: Duration(milliseconds: 800),
                    autoPlayCurve: Curves.easeInOutCubic,
                    enlargeCenterPage: enlargeCenterPage,
                    enlargeFactor: enlargeFactor,
                    viewportFraction: viewportFraction,
                    enableInfiniteScroll: true,
                  ),
                  itemBuilder: (context, index, realIndex) {
                    final imageData = allImages[index];
                    final MyProduct product = imageData['product'];
                    final String imageUrl = imageData['imageUrl'];

                    return _buildProductCard(context, product, imageUrl);
                  },
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProductCard(BuildContext context, MyProduct product, String imageUrl) {
    return Semantics(
      label: 'منتج ${product.name} بسعر ${product.getSafePrice()}',
      hint: 'اضغط لعرض تفاصيل المنتج',
      button: true,
      child: GestureDetector(
        onTap: () => onProductTap(product),
        child: Container(
          // margin: EdgeInsets.symmetric(horizontal: 8), // Removed. CarouselSlider's viewportFraction and padding manage spacing.
          decoration: DesignConstants.cardDecoration(
            backgroundColor: Theme.of(context).cardColor,
            isDark: Theme.of(context).brightness == Brightness.dark,
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(
                flex: 3,
                child: ClipRRect(
                  borderRadius: BorderRadius.vertical(top: Radius.circular(12)),
                  child: Semantics(
                    label: 'صورة المنتج ${product.name}',
                    child: CachedImageWidget(
                      image: imageUrl.isNotEmpty
                          ? imageUrl
                          : 'public/images/placeholder.jpg',
                      fit: BoxFit.contain,
                      height: double.infinity,
                      width: double.infinity,
                      placeholder: Center(
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          backgroundColor: ThemeColor.get(context).borderSecondary,
                          color: ThemeColor.get(context).textMuted,
                        ),
                      ),
                    ),
                  ),
                ),
              ),
              // Product Info Section
              Expanded(
                flex: 1,
                child: Padding(
                  padding: EdgeInsets.all(8),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Expanded(
                        child: Text(
                          product.name,
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            fontWeight: FontWeight.w600,
                            fontSize: 10,
                            height: 1.5,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                      // UNIFIED & GUARANTEED FIX - For ProductCarouselSectionWidget and ProductItemContainer
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          // A product is on sale if the standard isOnSale() is true OR if it's a variable product
                          // with a lower current price than its lowest regular price from variations.
                          // This assumes getLowestRegularPriceFromVariations() functions correctly as per the mission protocol.
                          if (product.isOnSale() || (product.type == "variable" && product.getSafePrice() < product.getLowestRegularPriceFromVariations(null) && product.getLowestRegularPriceFromVariations(null) > 0))
                            Padding(
                              // Added padding for visual separation of the two prices.
                              padding: const EdgeInsets.only(bottom: 2.0),
                              child: Text(
                                // CRITICAL LOGIC: For a variable product, the strikethrough price is its lowest regular price.
                                // For a simple product, it's the standard regular price.
                                formatStringCurrency(
                                  total: product.type == "variable"
                                      ? product.getLowestRegularPriceFromVariations(null)
                                      : product.getSafeRegularPrice()
                                ),
                                style: Theme.of(context).textTheme.bodySmall!.copyWith(
                                  decoration: TextDecoration.lineThrough,
                                  color: Colors.grey,
                                  fontSize: 8,
                                  height: 1.2, // Adjusted for tighter visual spacing.
                                ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                          // Display the current/sale price.
                          Text(
                            // For variable products, always show "From" to indicate a starting price.
                            product.type == "variable"
                              ? "من ${formatStringCurrency(total: product.getSafePrice())}"
                              // For simple products, show the definitive sale price if on sale, otherwise the regular price.
                              : formatStringCurrency(total: product.isOnSale() ? product.getSafeSalePrice() : product.getSafePrice()),
                            style: Theme.of(context).textTheme.bodySmall!.copyWith(
                              // The sale color is applied if any sale condition is met.
                              color: product.isOnSale() || (product.type == "variable" && product.getSafePrice() < product.getLowestRegularPriceFromVariations(null) && product.getLowestRegularPriceFromVariations(null) > 0)
                                     ? Colors.red
                                     : Theme.of(context).colorScheme.primary,
                              fontWeight: FontWeight.bold,
                              fontSize: 10,
                              height: 1.2, // Adjusted for tighter visual spacing.
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
