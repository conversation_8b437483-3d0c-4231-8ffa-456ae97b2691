//  Velvete Store
//
//  Created by we<PERSON>m Jr.
//  2025, Velvete Ltd. All rights reserved.
//

//  Unless required by applicable law or agreed to in writing, software
//  distributed under the License is distributed on an "AS IS" BASIS,
//  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

import 'package:flutter/material.dart';
import '/bootstrap/helpers.dart';

import '/app/models/woocommerce_wrappers/my_product.dart';
import '/app/models/woocommerce_wrappers/my_product_variation.dart';

class ProductDetailHeaderWidget extends StatelessWidget {
  const ProductDetailHeaderWidget({super.key, required this.product, this.variations, this.selectedVariation});

  final MyProduct? product;
  final List<dynamic>? variations;
  final MyProductVariation? selectedVariation;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(
        vertical: 10,
        horizontal: 16,
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: <Widget>[
          Flexible(
            flex: 4,
            child: Text(
              product?.name ?? "",
              style:
                  Theme.of(context).textTheme.bodyLarge!.copyWith(fontSize: 20),
              textAlign: TextAlign.left,
              overflow: TextOverflow.ellipsis,
              maxLines: 2,
            ),
          ),
          Flexible(
            flex: 2,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // Enhanced sale logic for variable products
                // Show original price first if on sale
                if ((selectedVariation != null && selectedVariation!.isOnSale()) ||
                    (selectedVariation == null && product?.isOnSale() == true))
                  Text(
                    formatStringCurrency(total: selectedVariation != null
                        ? selectedVariation!.getSafeRegularPrice()
                        : product?.getSafeRegularPrice()),
                    style: TextStyle(
                      color: Colors.grey,
                      decoration: TextDecoration.lineThrough,
                      fontSize: 16,
                    ),
                    textAlign: TextAlign.right,
                  ),
                // Current/Sale Price with variable product support
                Text(
                  formatStringCurrency(total: selectedVariation != null
                      ? (selectedVariation!.isOnSale()
                          ? selectedVariation!.getSafeSalePrice()
                          : selectedVariation!.getSafePrice())
                      : (product?.isOnSale() == true
                          ? product?.getSafeSalePrice()
                          : product?.getSafePrice())),
                  style: Theme.of(context).textTheme.headlineMedium!.copyWith(
                        fontSize: 20,
                        color: ((selectedVariation != null && selectedVariation!.isOnSale()) ||
                            (selectedVariation == null && product?.isOnSale() == true))
                            ? Colors.red : null,
                      ),
                  textAlign: TextAlign.right,
                ),
              ],
            ),
          )
        ],
      ),
    );
  }
}
