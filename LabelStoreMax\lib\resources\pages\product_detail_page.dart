// Velvete Store
//
// Created by <PERSON><PERSON>.
// Copyright © 2025, <PERSON><PERSON>. All rights reserved.
//
// This software is proprietary and confidential.
// Unauthorized copying, redistribution, or use of this software, in whole or in part,
// is strictly prohibited without the express written permission of <PERSON><PERSON>.
//
// All intellectual property rights, including copyrights, patents, trademarks,
// and trade secrets, in and to the software are owned by <PERSON><PERSON>.
//
// THE SOFTWARE IS PROVIDED "AS IS" WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED,
// INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A
// PARTICULAR PURPOSE, AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR
// COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES, OR OTHER LIABILITY,
// WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
// OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.



import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart'; // For kDebugMode diagnostic logging
import 'dart:async'; // PHASE 3: For Timer functionality
import '/resources/widgets/wishlist_icon_widget.dart';
import '/app/controllers/product_detail_controller.dart';
import '/app/models/cart_line_item.dart';
import '/bootstrap/helpers.dart';
import '/resources/widgets/buttons.dart';
import '/resources/widgets/velvete_ui.dart';
import '/resources/themes/styles/design_constants.dart';
import 'package:nylo_framework/nylo_framework.dart';
import 'package:flutter_widget_from_html_core/flutter_widget_from_html_core.dart';

import '/app/models/woocommerce_wrappers/my_product_variation.dart';
import '/app/models/woocommerce_wrappers/my_product.dart';
import '/app/services/woocommerce_service.dart';
import '/resources/widgets/cached_image_widget.dart';

class ProductDetailPage extends NyStatefulWidget<ProductDetailController> {
  static RouteView path = ("/product-detail", (_) => ProductDetailPage());

  ProductDetailPage({super.key}) : super(child: () => _ProductDetailState());
}

class _ProductDetailState extends NyPage<ProductDetailPage> {
  MyProduct? _product;

  List<MyProductVariation> _productVariations = [];
  final Map<int, dynamic> _tmpAttributeObj = {};
  final Map<String, String> _selectedAttributes = {}; // Track selected attributes
  MyProductVariation? _selectedVariation; // PHASE 2.2: Track selected variation for dynamic price/stock

  // PHASE 3: Image carousel enhancements
  PageController _pageController = PageController();
  int _currentImageIndex = 0;
  Timer? _autoScrollTimer;
  bool _userInteracting = false;

  // Remove WooSignalApp reference for now

  @override
  get init => () async {
        final data = widget.controller.data();
        WooCommerceService wooCommerceService = WooCommerceService();
        if (data is Map && data.containsKey("productId")) {
          _product = await wooCommerceService.getProduct(data["productId"]);
        } else {
          _product = data;
        }
        widget.controller.product = _product;
        if (_product?.type == "variable") {
          await _fetchProductVariations();
        }
        _startAutoScroll(); // PHASE 3: Start auto-scroll for image carousel
      };

  @override
  void dispose() {
    _autoScrollTimer?.cancel(); // PHASE 3: Clean up timer
    _pageController.dispose();
    super.dispose();
  }

  @override
  LoadingStyle loadingStyle = LoadingStyle.skeletonizer();

  String _getAttributeName(int index) {
    if (_product?.attributes == null || index >= _product!.attributes.length) {
      return "";
    }

    final attribute = _product!.attributes[index];
    // Handle both MyProductAttribute objects and raw Map data
    if (attribute is Map<String, dynamic>) {
      return attribute['name']?.toString() ?? "";
    } else {
      // This should be a MyProductAttribute object with a name property
      try {
        return attribute.name?.toString() ?? "";
      } catch (e) {
        print('⚠️ Error accessing attribute name: $e');
        // Fallback: try to convert to string and extract name
        String attrStr = attribute.toString();
        return attrStr.isNotEmpty ? attrStr : "Unknown Attribute";
      }
    }
  }

  List<String> _getAttributeOptions(int index) {
    if (_product?.attributes == null || index >= _product!.attributes.length) {
      return [];
    }

    final attribute = _product!.attributes[index];
    if (attribute is Map<String, dynamic>) {
      final options = attribute['options'];
      if (options is List) {
        return options.map((option) => option.toString()).toList();
      }
    } else if (attribute != null) {
      // Try to access options property if it exists
      try {
        final options = attribute.options;
        if (options is List) {
          return options.map((option) => option.toString()).toList();
        }
      } catch (e) {
        // Fallback
      }
    }
    return [];
  }

  _fetchProductVariations() async {
    List<MyProductVariation> tmpVariations = [];
    int currentPage = 1;
    WooCommerceService wooCommerceService = WooCommerceService();

    bool isFetching = true;
    if (_product?.id == null) {
      return;
    }
    while (isFetching) {
      List<MyProductVariation> tmp = await wooCommerceService.getProductVariations(
        _product!.id,
        page: currentPage,
        perPage: 100,
      );
      if (tmp.isNotEmpty) {
        tmpVariations.addAll(tmp);
      }

      if (tmp.length >= 100) {
        currentPage += 1;
      } else {
        isFetching = false;
      }
    }
    _productVariations = tmpVariations;
  }

  // PHASE 2.2: Method to update selected variation and refresh UI
  void _updateSelectedVariation() {
    if (_product?.type != "variable" || _productVariations.isEmpty) {
      _selectedVariation = null;
      return;
    }

    // Convert selected attributes to the format expected by the variation finder
    Map<int, dynamic> attributeObj = {};
    for (int i = 0; i < _product!.attributes.length; i++) {
      String attributeName = _getAttributeName(i);
      if (_selectedAttributes.containsKey(attributeName)) {
        attributeObj[i] = {
          "name": attributeName,
          "value": _selectedAttributes[attributeName]
        };
      }
    }

    // Find the matching product variation
    MyProductVariation? foundVariation = widget.controller.findProductVariation(
      tmpAttributeObj: attributeObj,
      productVariations: _productVariations,
    );

    setState(() {
      _selectedVariation = foundVariation;
    });
  }

  // PHASE 3: Auto-scroll functionality for image carousel
  void _startAutoScroll() {
    if (_product?.images == null || _product!.images.length <= 1) return;

    _autoScrollTimer = Timer.periodic(Duration(seconds: 4), (timer) {
      if (!_userInteracting && mounted) {
        int nextIndex = (_currentImageIndex + 1) % _product!.images.length;
        _pageController.animateToPage(
          nextIndex,
          duration: Duration(milliseconds: 800),
          curve: Curves.easeInOutCubic,
        );
        setState(() {
          _currentImageIndex = nextIndex;
        });
      }
    });
  }

  void _stopAutoScroll() {
    _autoScrollTimer?.cancel();
  }

  void _resumeAutoScroll() {
    _stopAutoScroll();
    Timer(Duration(seconds: 2), () {
      if (mounted) {
        setState(() {
          _userInteracting = false;
        });
        _startAutoScroll();
      }
    });
  }

  _modalBottomSheetOptionsForAttribute(int attributeIndex) {
    wsModalBottom(
      context,
      (BuildContext context) => Container(
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.surface,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        padding: EdgeInsets.all(20),
        constraints: BoxConstraints(
          maxHeight: MediaQuery.of(context).size.height * 0.7,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header with close button
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  "${trans("Select")} ${_getAttributeName(attributeIndex)}",
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).colorScheme.onSurface,
                  ),
                ),
                IconButton(
                  onPressed: () => Navigator.pop(context),
                  icon: Icon(Icons.close, color: ThemeColor.get(context).textSecondary),
                ),
              ],
            ),
            Divider(color: ThemeColor.get(context).borderSecondary),
            SizedBox(height: 8),
            Expanded(
              child: ListView.builder(
                shrinkWrap: true,
                itemCount: _getAttributeOptions(attributeIndex).length,
                itemBuilder: (BuildContext context, int index) {
                  final options = _getAttributeOptions(attributeIndex);
                  final optionValue = options[index];
                  final isSelected = (_tmpAttributeObj.isNotEmpty &&
                      _tmpAttributeObj.containsKey(attributeIndex) &&
                      _tmpAttributeObj[attributeIndex]["value"] == optionValue);

                  return Container(
                    margin: EdgeInsets.symmetric(vertical: 4),
                    decoration: BoxDecoration(
                      color: isSelected
                          ? Theme.of(context).primaryColor
                          : ThemeColor.get(context).surfaceSecondary,
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: isSelected
                            ? Theme.of(context).primaryColor
                            : ThemeColor.get(context).borderPrimary,
                        width: isSelected ? 2 : 1,
                      ),
                      boxShadow: isSelected ? [
                        BoxShadow(
                          color: Theme.of(context).primaryColor.withValues(alpha: 0.3),
                          blurRadius: 8,
                          offset: Offset(0, 2),
                        ),
                      ] : null,
                    ),
                    child: Material(
                      color: Colors.transparent,
                      child: InkWell(
                        borderRadius: BorderRadius.circular(12),
                        onTap: () {
                          _tmpAttributeObj[attributeIndex] = {
                            "name": _getAttributeName(attributeIndex),
                            "value": optionValue
                          };
                          Navigator.pop(context);
                          _modalBottomSheetAttributes();
                        },
                        child: Padding(
                          padding: EdgeInsets.symmetric(horizontal: 16, vertical: 16),
                          child: Row(
                            children: [
                              Expanded(
                                child: Text(
                                  optionValue,
                                  style: TextStyle(
                                    fontSize: 16,
                                    fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
                                    color: isSelected ? Theme.of(context).colorScheme.onPrimary : Theme.of(context).colorScheme.onSurface,
                                  ),
                                ),
                              ),
                              if (isSelected)
                                Icon(
                                  Icons.check_circle,
                                  color: Theme.of(context).colorScheme.onPrimary,
                                  size: 24,
                                ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  _modalBottomSheetAttributes() {
    MyProductVariation? productVariation = widget.controller
        .findProductVariation(
            tmpAttributeObj: _tmpAttributeObj,
            productVariations: _productVariations);
    wsModalBottom(
      context,
      (BuildContext context) => Container(
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.surface,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        padding: EdgeInsets.all(20),
        constraints: BoxConstraints(
          maxHeight: MediaQuery.of(context).size.height * 0.8,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  trans("Product Options"),
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).colorScheme.onSurface,
                  ),
                ),
                IconButton(
                  onPressed: () => Navigator.pop(context),
                  icon: Icon(Icons.close, color: Colors.grey[600]),
                ),
              ],
            ),
            Divider(color: Colors.grey[300]),
            SizedBox(height: 8),
            Expanded(
              child: ListView.builder(
                shrinkWrap: true,
                itemCount: _product?.attributes.length ?? 0,
                itemBuilder: (BuildContext context, int index) {
                  final isSelected = (_tmpAttributeObj.isNotEmpty &&
                      _tmpAttributeObj.containsKey(index));
                  final attributeName = _getAttributeName(index);

                  return Container(
                    margin: EdgeInsets.symmetric(vertical: 6),
                    decoration: BoxDecoration(
                      color: Colors.grey[50],
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: isSelected
                            ? Theme.of(context).primaryColor
                            : Colors.grey[300]!,
                        width: isSelected ? 2 : 1,
                      ),
                    ),
                    child: Material(
                      color: Colors.transparent,
                      child: InkWell(
                        borderRadius: BorderRadius.circular(12),
                        onTap: () => _modalBottomSheetOptionsForAttribute(index),
                        child: Padding(
                          padding: EdgeInsets.all(16),
                          child: Row(
                            children: [
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      attributeName,
                                      style: TextStyle(
                                        fontSize: 16,
                                        fontWeight: FontWeight.w600,
                                        color: Theme.of(context).colorScheme.onSurface,
                                      ),
                                    ),
                                    SizedBox(height: 4),
                                    Text(
                                      isSelected
                                          ? _tmpAttributeObj[index]["value"]
                                          : "${trans("Tap to select")} $attributeName",
                                      style: TextStyle(
                                        fontSize: 14,
                                        color: isSelected
                                            ? Theme.of(context).primaryColor
                                            : Colors.grey[600],
                                        fontWeight: isSelected ? FontWeight.w500 : FontWeight.normal,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              Icon(
                                isSelected ? Icons.check_circle : Icons.arrow_forward_ios,
                                color: isSelected
                                    ? Theme.of(context).primaryColor
                                    : Colors.grey[400],
                                size: isSelected ? 24 : 16,
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  );
                },
              ),
            ),
            Container(
              decoration: BoxDecoration(
                  border: Border(top: BorderSide(color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2), width: 1))),
              padding: EdgeInsets.only(top: 10),
              margin: EdgeInsets.only(bottom: 10),
              child: Column(
          children: <Widget>[
            Text(
              (productVariation != null
                  ? "${trans("Price")}: ${formatStringCurrency(total: productVariation.getSafePrice())}"
                  : (((_product?.attributes.length ==
                              _tmpAttributeObj.values.length) &&
                          productVariation == null)
                      ? trans("This variation is unavailable")
                      : trans("Choose your options"))),
              style: Theme.of(context).textTheme.titleMedium,
            ),
            Text(
              (productVariation != null
                  ? !productVariation.isInStock()
                      ? trans("Out of stock")
                      : ""
                  : ""),
              style: Theme.of(context).textTheme.titleMedium,
            ),
            PrimaryButton(
                title: trans("Add to cart"),
                action: () async {
                  // DEFINITIVE ACTION 2: Fortify the "Add to Cart" Gates - ROBUST VALIDATION
                  if (_product?.id == null || _product?.id == 0) {
                    print('❌ DEFINITIVE ACTION 2: BLOCKED invalid product ID: ${_product?.id}');
                    showToast(
                      title: trans("Error"),
                      description: trans("Invalid product cannot be added to cart. Please try again."),
                      style: ToastNotificationStyleType.danger,
                    );
                    return; // IMMEDIATE STOP - Do not proceed to create CartLineItem
                  }

                  if (_product?.attributes.length !=
                      _tmpAttributeObj.values.length) {
                    showToast(
                        title: trans("Oops"),
                        description: trans("Please select valid options first"),
                        style: ToastNotificationStyleType.warning);
                    return;
                  }

                  if (productVariation == null) {
                    showToast(
                        title: trans("Oops"),
                        description: trans("Product variation does not exist"),
                        style: ToastNotificationStyleType.warning);
                    return;
                  }

                  if (!productVariation.isInStock()) {
                    showToast(
                        title: trans("Sorry"),
                        description: trans("This item is not in stock"),
                        style: ToastNotificationStyleType.warning);
                    return;
                  }

                  List<String> options = [];
                  _tmpAttributeObj.forEach((k, v) {
                    options.add("${v["name"]}: ${v["value"]}");
                  });

                  print('🛒 Creating CartLineItem for variable product: ${_product!.name} (ID: ${_product!.id})');

                  CartLineItem cartLineItem = CartLineItem.fromProductVariation(
                    quantityAmount: widget.controller.quantity,
                    options: options,
                    product: _product!,
                    productVariation: productVariation,
                  );

                  print('✅ CartLineItem created with productId: ${cartLineItem.productId}');

                  await widget.controller.itemAddToCart(
                    cartLineItem: cartLineItem,
                  );
                  pop();
                }),
              ],
            ),
          ),
          ],
        ),
      ),
    );
  }

  @override
  Widget view(BuildContext context) {
    if (_product == null) {
      return Scaffold(
        body: Center(
          child: CircularProgressIndicator(
            color: Theme.of(context).colorScheme.primary,
          ),
        ),
      );
    }

    return Scaffold(
      body: NestedScrollView(
        headerSliverBuilder: (BuildContext context, bool innerBoxIsScrolled) {
          return <Widget>[
            SliverAppBar(
              backgroundColor: Theme.of(context).scaffoldBackgroundColor.withValues(
                alpha: innerBoxIsScrolled ? 0.95 : 1.0,
              ),
              elevation: innerBoxIsScrolled ? 4 : 0,
              leading: IconButton(
                icon: Icon(
                  Icons.arrow_back,
                  color: Theme.of(context).iconTheme.color,
                ),
                onPressed: () => Navigator.pop(context),
              ),
              actions: [
                IconButton(
                  icon: Icon(
                    Icons.more_vert,
                    color: Theme.of(context).iconTheme.color,
                  ),
                  onPressed: () {
                    _showMoreOptions(context);
                  },
                ),
                WishlistIcon(_product),
                IconButton(
                  icon: Icon(
                    Icons.close,
                    color: Theme.of(context).iconTheme.color,
                  ),
                  onPressed: () => Navigator.pop(context),
                ),
              ],
              floating: true,
              snap: true,
              pinned: true,
              expandedHeight: 0,
            ),
          ];
        },
        body: Stack(
          children: [
            // Main content with enhanced scroll effects
            CustomScrollView(
              slivers: [
                SliverToBoxAdapter(
                  child: Column(
                    children: [
                      _buildImageGallery(context),
                      _buildProductInfo(context),
                      _buildCollapsibleSections(context),
                      SizedBox(height: 100), // Space for bottom action bar
                    ],
                  ),
                ),
              ],
            ),
          // Sticky bottom action bar
          Positioned(
            bottom: 0,
            left: 0,
            right: 0,
            child: _buildBottomActionBar(context),
          ),
        ],
      ),
      ),
    );
  }

  _addItemToCart() async {
    // DEFINITIVE ACTION 2: Fortify the "Add to Cart" Gates - ROBUST VALIDATION
    if (_product?.id == null || _product?.id == 0) {
      print('❌ DEFINITIVE ACTION 2: BLOCKED invalid product ID: ${_product?.id}');
      showToast(
        title: trans("Error"),
        description: trans("Invalid product cannot be added to cart. Please try again."),
        style: ToastNotificationStyleType.danger,
      );
      return; // IMMEDIATE STOP - Do not proceed to create CartLineItem
    }

    // Handle variable products with visible attribute selection
    if (_product?.type == "variable") {
      await _addVariableProductToCart();
      return;
    }

    // Handle simple products
    if (_product?.stockStatus != "instock") {
      showToast(
          title: trans("Sorry"),
          description: trans("This item is out of stock"),
          style: ToastNotificationStyleType.warning,
          icon: Icons.local_shipping);
      return;
    }

    print('🛒 Adding simple product to cart: ${_product!.name} (ID: ${_product!.id})');

    await widget.controller.itemAddToCart(
      cartLineItem: CartLineItem.fromProduct(
          quantityAmount: widget.controller.quantity, product: _product!),
    );
  }

  _addVariableProductToCart() async {
    // Check if all required attributes are selected
    if (_product?.attributes.isEmpty ?? true) {
      showToast(
        title: trans("Error"),
        description: trans("Product has no attributes"),
        style: ToastNotificationStyleType.danger,
      );
      return;
    }

    // Validate that all attributes are selected
    for (var attribute in _product!.attributes) {
      String attributeName = '';
      // PHASE 2.2 FIX: Handle attribute as Map<String, dynamic>
      if (attribute is Map<String, dynamic>) {
        attributeName = attribute['name']?.toString() ?? '';
      } else {
        // Fallback for object-style attributes
        try {
          attributeName = attribute.name?.toString() ?? '';
        } catch (e) {
          print('⚠️ Error accessing attribute name: $e');
          attributeName = '';
        }
      }

      if (!_selectedAttributes.containsKey(attributeName) ||
          _selectedAttributes[attributeName]?.isEmpty == true) {
        showToast(
          title: trans("Oops"),
          description: trans("Please select all product options first"),
          style: ToastNotificationStyleType.warning,
        );
        return;
      }
    }

    // Convert selected attributes to the format expected by the variation finder
    Map<int, dynamic> attributeObj = {};
    for (int i = 0; i < _product!.attributes.length; i++) {
      String attributeName = _getAttributeName(i);
      if (_selectedAttributes.containsKey(attributeName)) {
        attributeObj[i] = {
          "name": attributeName,
          "value": _selectedAttributes[attributeName]
        };
      }
    }

    // PHASE 2.2: Use the already selected variation for better performance
    MyProductVariation? productVariation = _selectedVariation;

    // Fallback: Find the matching product variation if not already selected
    if (productVariation == null) {
      productVariation = widget.controller.findProductVariation(
        tmpAttributeObj: attributeObj,
        productVariations: _productVariations,
      );
    }

    if (productVariation == null) {
      showToast(
        title: trans("Oops"),
        description: trans("This variation is not available"),
        style: ToastNotificationStyleType.warning,
      );
      return;
    }

    if (!productVariation.isInStock()) {
      showToast(
        title: trans("Sorry"),
        description: trans("This item is not in stock"),
        style: ToastNotificationStyleType.warning,
      );
      return;
    }

    // Create options list for display
    List<String> options = [];
    attributeObj.forEach((k, v) {
      options.add("${v["name"]}: ${v["value"]}");
    });

    print('🛒 Adding variable product to cart: ${_product!.name} (ID: ${_product!.id}, Variation: ${productVariation.id})');

    CartLineItem cartLineItem = CartLineItem.fromProductVariation(
      quantityAmount: widget.controller.quantity,
      options: options,
      product: _product!,
      productVariation: productVariation,
    );

    await widget.controller.itemAddToCart(cartLineItem: cartLineItem);
  }



  Widget _buildImageGallery(BuildContext context) {
    List<String> images = [];

    // Add main image
    final productImages = _product?.images;
    if (productImages?.isNotEmpty == true) {
      images.addAll(productImages!.map((img) => img.src ?? '').where((src) => src.isNotEmpty));
    }

    // If no images, add placeholder
    if (images.isEmpty) {
      images.add(''); // Placeholder
    }

    return Container(
      height: 400,
      child: Stack(
        children: [
          // Main PageView with enhanced functionality
          PageView.builder(
            controller: _pageController,
            itemCount: images.length,
            onPageChanged: (index) {
              setState(() {
                _currentImageIndex = index;
                _userInteracting = true;
              });
              _resumeAutoScroll(); // Resume auto-scroll after user interaction
            },
            itemBuilder: (context, index) {
              return Container(
                margin: EdgeInsets.symmetric(horizontal: 8),
                decoration: BoxDecoration(
                  color: Theme.of(context).brightness == Brightness.dark
                      ? Colors.grey[800]
                      : Colors.grey[100],
                  borderRadius: BorderRadius.circular(16),
                ),
                child: images[index].isNotEmpty
                    ? ClipRRect(
                        borderRadius: BorderRadius.circular(16),
                        child: Hero(
                          tag: index == 0 ? 'product_detail_image_${_product?.id}' : 'product_detail_image_${_product?.id}_$index',
                          child: CachedImageWidget(
                            image: images[index],
                            fit: BoxFit.cover,
                            height: double.infinity,
                            width: double.infinity,
                            placeholder: Center(
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                backgroundColor: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
                                color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
                              ),
                            ),
                          ),
                        ),
                      )
                    : _buildImagePlaceholder(),
              );
            },
          ),

          // PHASE 3: Navigation arrows with dynamic visibility
          if (images.length > 1) ...[
            // Left arrow
            if (_currentImageIndex > 0)
              Positioned(
                left: 16,
                top: 0,
                bottom: 0,
                child: Center(
                  child: GestureDetector(
                    onTap: () {
                      setState(() {
                        _userInteracting = true;
                      });
                      _pageController.previousPage(
                        duration: Duration(milliseconds: 300),
                        curve: Curves.easeInOut,
                      );
                      _resumeAutoScroll();
                    },
                    child: Container(
                      width: 40,
                      height: 40,
                      decoration: BoxDecoration(
                        color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
                        shape: BoxShape.circle,
                      ),
                      child: Icon(
                        Icons.chevron_left,
                        color: Theme.of(context).colorScheme.onSurface,
                        size: 24,
                      ),
                    ),
                  ),
                ),
              ),

            // Right arrow
            if (_currentImageIndex < images.length - 1)
              Positioned(
                right: 16,
                top: 0,
                bottom: 0,
                child: Center(
                  child: GestureDetector(
                    onTap: () {
                      setState(() {
                        _userInteracting = true;
                      });
                      _pageController.nextPage(
                        duration: Duration(milliseconds: 300),
                        curve: Curves.easeInOut,
                      );
                      _resumeAutoScroll();
                    },
                    child: Container(
                      width: 40,
                      height: 40,
                      decoration: BoxDecoration(
                        color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
                        shape: BoxShape.circle,
                      ),
                      child: Icon(
                        Icons.chevron_right,
                        color: Theme.of(context).colorScheme.onSurface,
                        size: 24,
                      ),
                    ),
                  ),
                ),
              ),
          ],

          // Page indicators
          if (images.length > 1)
            Positioned(
              bottom: 16,
              left: 0,
              right: 0,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: images.asMap().entries.map((entry) {
                  return Container(
                    width: 8,
                    height: 8,
                    margin: EdgeInsets.symmetric(horizontal: 4),
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: _currentImageIndex == entry.key
                          ? Theme.of(context).colorScheme.primary
                          : Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
                    ),
                  );
                }).toList(),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildImagePlaceholder() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.grey[200],
        borderRadius: BorderRadius.circular(16),
      ),
      child: Center(
        child: Icon(
          Icons.image,
          size: 80,
          color: Colors.grey[400],
        ),
      ),
    );
  }

  Widget _buildProductInfo(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Product Name
          Text(
            _product?.name ?? '',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: Theme.of(context).textTheme.headlineSmall?.color,
                ),
            textAlign: TextAlign.right,
          ),

          SizedBox(height: 8),

          // SKU and Availability
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              _buildStockDisplay(),
              if (_product?.sku?.isNotEmpty == true)
                Text(
                  '${trans("Product Code")}: ${_product!.sku}',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Colors.grey[600],
                      ),
                ),
            ],
          ),

          SizedBox(height: 16),

          // Price
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              _buildQuantitySelector(),
              _buildPriceDisplay(),
            ],
          ),

          SizedBox(height: 20),

          // Product Attributes Selection (Prominent Display)
          _buildAttributeSelection(),
        ],
      ),
    );
  }

  Widget _buildPriceDisplay() {
    String currentPrice = '';
    bool showSalePrice = false;
    String? regularPriceText;

    // DIAGNOSTIC LOGGING: Log product information at start of _buildPriceDisplay
    if (kDebugMode) {
      print('🔍 _buildPriceDisplay for Product ID ${_product?.id}: _product.isOnSale()=${_product?.isOnSale()}, type=${_product?.type}');
    }

    // PHASE 2.2: Dynamic price display based on selected variation
    if (_product?.type == "variable") {
      if (_selectedVariation != null) {
        // DIAGNOSTIC LOGGING: Log selected variation information
        if (kDebugMode) {
          print('🔍 _buildPriceDisplay: Selected variation ${_selectedVariation!.id}, isOnSale()=${_selectedVariation!.isOnSale()}');
        }

        // Show selected variation price with discount
        if (_selectedVariation!.isOnSale()) {
          currentPrice = formatStringCurrency(total: _selectedVariation!.getSafeSalePrice());
          regularPriceText = formatStringCurrency(total: _selectedVariation!.getSafeRegularPrice());
          showSalePrice = true;
        } else {
          currentPrice = formatStringCurrency(total: _selectedVariation!.getSafePrice());
        }
      } else {
        // CORRECTED LOGIC for when NO variation is selected (initial load or unselected)
        // Fix the core logical flaw in price display for variable products

        if (_productVariations.isNotEmpty) {
          // Check if any variations are on sale using the hasAnyVariationOnSale method
          bool hasVariationsOnSale = _product?.hasAnyVariationOnSale(_productVariations) ?? false;

          if (hasVariationsOnSale) {
            // At least one variation is on sale - show minimum sale price WITHOUT "من" prefix
            String? minimumSalePrice = _product?.getMinimumSalePrice(_productVariations);
            num regularPriceFromVariations = _product?.getLowestRegularPriceFromVariations(_productVariations) ?? 0.0;

            if (minimumSalePrice != null && minimumSalePrice.isNotEmpty) {
              currentPrice = formatStringCurrency(total: minimumSalePrice);
              regularPriceText = formatStringCurrency(total: regularPriceFromVariations);
              showSalePrice = true;
            } else {
              // Fallback if getMinimumSalePrice returns null/empty
              num priceFromVariations = _product?.getPriceFromVariations(_productVariations) ?? 0.0;
              currentPrice = "من " + formatStringCurrency(total: priceFromVariations);
              showSalePrice = false;
              regularPriceText = null;
            }
          } else {
            // No variations are on sale - show regular price range with "من" prefix
            num regularPriceFromVariations = _product?.getLowestRegularPriceFromVariations(_productVariations) ?? 0.0;
            currentPrice = "من " + formatStringCurrency(total: regularPriceFromVariations);
            showSalePrice = false;
            regularPriceText = null;
          }
        } else {
          // No variations available
          currentPrice = "اختر المواصفات للسعر";
          showSalePrice = false;
          regularPriceText = null;
        }
      }
    } else {
      // Simple product pricing - PHASE 11: Use robust getSafe methods
      if (_product?.isOnSale() == true) {
        currentPrice = formatStringCurrency(total: _product!.getSafeSalePrice());
        regularPriceText = formatStringCurrency(total: _product!.getSafeRegularPrice());
        showSalePrice = true;
      } else {
        currentPrice = formatStringCurrency(total: _product!.getSafePrice());
      }
    }

    // DIAGNOSTIC LOGGING: Log final calculated values before returning widget
    if (kDebugMode) {
      print('🔍 _buildPriceDisplay for Product ID ${_product?.id}: currentPrice=$currentPrice, showSalePrice=$showSalePrice, regularPriceText=$regularPriceText');
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.end,
      children: [
        // Show original price first if on sale
        if (showSalePrice && regularPriceText != null)
          Text(
            regularPriceText,
            style: TextStyle(
              fontSize: 18,
              decoration: TextDecoration.lineThrough,
              color: ThemeColor.get(context).textSecondary,
            ),
          ),
        // Current/Sale Price
        Text(
          currentPrice,
          style: TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: showSalePrice ? ThemeColor.get(context).errorColor : Theme.of(context).colorScheme.primary,
          ),
        ),
      ],
    );
  }

  // PHASE 2.2: Dynamic stock display based on selected variation
  Widget _buildStockDisplay() {
    // For variable products, hide the availability text until a variation is selected
    if (_product?.type == "variable" && _selectedVariation == null) {
      return SizedBox.shrink(); // Hides the widget
    }

    bool isInStock = false;
    int? stockQuantity;

    if (_product?.type == "variable") {
      if (_selectedVariation != null) {
        isInStock = _selectedVariation!.isInStock();
        stockQuantity = _selectedVariation!.stockQuantity;
      }
    } else {
      isInStock = _product?.stockStatus == 'instock';
      stockQuantity = _product?.stockQuantity;
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          isInStock ? trans('Available') : trans('Not Available'),
          style: TextStyle(
            color: isInStock ? ThemeColor.get(context).successColor : ThemeColor.get(context).errorColor,
            fontWeight: FontWeight.w600,
          ),
        ),
        if (isInStock && stockQuantity != null && stockQuantity > 0)
          Text(
            '${trans("Available Quantity")}: $stockQuantity',
            style: TextStyle(
              color: Colors.grey[600],
              fontSize: 12,
            ),
          ),
      ],
    );
  }

  Widget _buildAttributeSelection() {
    // Only show for variable products with attributes
    if (_product?.type != 'variable' || _product?.attributes == null || _product!.attributes.isEmpty) {
      return SizedBox.shrink();
    }

    return Container(
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).brightness == Brightness.dark
            ? Colors.grey[800]
            : Colors.grey[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Theme.of(context).brightness == Brightness.dark
              ? Colors.grey[600]!
              : Colors.grey[200]!,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'اختر المواصفات',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: Theme.of(context).textTheme.titleMedium?.color,
                ),
          ),
          SizedBox(height: 12),

          // Display each attribute with selection options
          ...(_product!.attributes).map((attr) {
            return _buildSingleAttributeSelector(attr);
          }).toList(),
        ],
      ),
    );
  }

  Widget _buildSingleAttributeSelector(dynamic attr) {
    String attributeName = '';
    List<String> attributeOptions = [];

    try {
      // Handle dynamic attribute structure safely
      if (attr is Map<String, dynamic>) {
        attributeName = attr['name']?.toString() ?? '';

        // Handle options array
        if (attr['options'] is List) {
          attributeOptions = (attr['options'] as List)
              .map((option) => option.toString())
              .toList();
        }
      }
    } catch (e) {
      print('Error parsing attribute: $e');
      return SizedBox.shrink();
    }

    if (attributeName.isEmpty || attributeOptions.isEmpty) {
      return SizedBox.shrink();
    }

    return Container(
      margin: EdgeInsets.only(bottom: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            attributeName,
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: Theme.of(context).textTheme.bodyLarge?.color,
                ),
          ),
          SizedBox(height: 8),

          // Options as chips
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: attributeOptions.map((option) {
              bool isSelected = _selectedAttributes[attributeName] == option;

              return GestureDetector(
                onTap: () {
                  setState(() {
                    _selectedAttributes[attributeName] = option;
                  });
                  // PHASE 2.2: Update selected variation when attribute changes
                  _updateSelectedVariation();
                },
                child: Container(
                  padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  decoration: BoxDecoration(
                    color: isSelected
                        ? Theme.of(context).colorScheme.primary
                        : Colors.transparent,
                    border: Border.all(
                      color: isSelected
                          ? Theme.of(context).colorScheme.primary
                          : Colors.grey[400]!,
                    ),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Text(
                    option,
                    style: TextStyle(
                      color: isSelected
                          ? Theme.of(context).colorScheme.onPrimary
                          : Theme.of(context).colorScheme.onSurface,
                      fontWeight: isSelected
                          ? FontWeight.w600
                          : FontWeight.normal,
                    ),
                  ),
                ),
              );
            }).toList(),
          ),
        ],
      ),
    );
  }

  Widget _buildQuantitySelector() {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey[300]!),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          IconButton(
            onPressed: () => widget.controller.removeQuantityTapped(),
            icon: Icon(Icons.remove_circle_outline, color: Theme.of(context).colorScheme.primary),
          ),
          Container(
            padding: EdgeInsets.symmetric(horizontal: 16),
            child: Text(
              '${widget.controller.quantity}',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          IconButton(
            onPressed: () => widget.controller.addQuantityTapped(),
            icon: Icon(Icons.add_circle_outline, color: Theme.of(context).colorScheme.primary),
          ),
        ],
      ),
    );
  }

  Widget _buildCollapsibleSections(BuildContext context) {
    return Column(
      children: [
        _buildCollapsibleSection(
          title: 'العلامة التجارية',
          content: _product?.name ?? 'غير محدد',
          icon: Icons.business,
        ),
        _buildCollapsibleSection(
          title: 'الوصف',
          content: _buildDescriptionWidget(),
          icon: Icons.description,
          isWidget: true,
        ),
        _buildCollapsibleSection(
          title: 'معلومات إضافية',
          content: _buildAdditionalInfo(),
          icon: Icons.info,
          isWidget: true,
        ),
      ],
    );
  }

  Widget _buildCollapsibleSection({
    required String title,
    required dynamic content,
    required IconData icon,
    bool isWidget = false,
  }) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).colorScheme.shadow.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: ExpansionTile(
        leading: Icon(
          icon,
          color: Theme.of(context).colorScheme.primary,
        ),
        title: Text(
          title,
          style: TextStyle(
            fontWeight: FontWeight.w600,
            color: Theme.of(context).colorScheme.onSurface,
          ),
        ),
        children: [
          Padding(
            padding: EdgeInsets.all(16),
            child: isWidget
                ? content as Widget
                : Text(
                    content as String,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          height: 1.5,
                        ),
                    textAlign: TextAlign.right,
                  ),
          ),
        ],
      ),
    );
  }

  Widget _buildAdditionalInfo() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Handle attributes safely
        if (_product?.attributes != null && _product!.attributes.isNotEmpty)
          ...(_product!.attributes).map((attr) {
            String attributeName = '';
            String attributeOptions = '';

            try {
              // Handle dynamic attribute structure safely
              if (attr is Map<String, dynamic>) {
                attributeName = attr['name']?.toString() ?? '';
                final options = attr['options'];
                if (options is List) {
                  attributeOptions = options.map((opt) => opt?.toString() ?? '').join(', ');
                } else if (options != null) {
                  attributeOptions = options.toString();
                }
              } else {
                // Try to access as object with properties
                try {
                  attributeName = attr.name?.toString() ?? '';
                  final options = attr.options;
                  if (options is List) {
                    attributeOptions = options.map((opt) => opt?.toString() ?? '').join(', ');
                  } else if (options != null) {
                    attributeOptions = options.toString();
                  }
                } catch (e) {
                  // Fallback: convert entire attribute to string
                  attributeName = 'خاصية';
                  attributeOptions = attr.toString();
                }
              }
            } catch (e) {
              print('⚠️ Error processing attribute: $e');
              attributeName = 'خاصية';
              attributeOptions = 'غير محدد';
            }

            // Only show if we have meaningful data
            if (attributeName.isNotEmpty || attributeOptions.isNotEmpty) {
              return Padding(
                padding: EdgeInsets.symmetric(vertical: 4),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Expanded(
                      child: Text(
                        attributeOptions.isNotEmpty ? attributeOptions : 'غير محدد',
                        style: TextStyle(color: Colors.grey[600]),
                        textAlign: TextAlign.left,
                      ),
                    ),
                    SizedBox(width: 8),
                    Text(
                      attributeName.isNotEmpty ? attributeName : 'خاصية',
                      style: TextStyle(fontWeight: FontWeight.w600),
                      textAlign: TextAlign.right,
                    ),
                  ],
                ),
              );
            } else {
              return SizedBox.shrink();
            }
          }).where((widget) => widget is! SizedBox),

        // Handle categories safely
        if (_product?.categories?.isNotEmpty == true)
          Padding(
            padding: EdgeInsets.symmetric(vertical: 4),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Text(
                    _product!.categories!.map((cat) => cat.name).join(', '),
                    style: TextStyle(color: Colors.grey[600]),
                    textAlign: TextAlign.left,
                  ),
                ),
                SizedBox(width: 8),
                Text(
                  'الفئات',
                  style: TextStyle(fontWeight: FontWeight.w600),
                  textAlign: TextAlign.right,
                ),
              ],
            ),
          ),

        // Show SKU if available
        if (_product?.sku?.isNotEmpty == true)
          Padding(
            padding: EdgeInsets.symmetric(vertical: 4),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Text(
                    _product!.sku!,
                    style: TextStyle(color: Colors.grey[600]),
                    textAlign: TextAlign.left,
                  ),
                ),
                SizedBox(width: 8),
                Text(
                  'رمز المنتج',
                  style: TextStyle(fontWeight: FontWeight.w600),
                  textAlign: TextAlign.right,
                ),
              ],
            ),
          ),
      ],
    );
  }

  Widget _buildBottomActionBar(BuildContext context) {
    // PHASE 2.2: Check if buttons should be enabled
    bool buttonsEnabled = true;
    String disabledReason = '';

    if (_product?.type == "variable") {
      if (_selectedVariation == null) {
        buttonsEnabled = false;
        disabledReason = 'يرجى اختيار المواصفات أولاً';
      } else if (!_selectedVariation!.isInStock()) {
        buttonsEnabled = false;
        disabledReason = 'هذا المنتج غير متوفر';
      }
    } else {
      if (_product?.stockStatus != 'instock') {
        buttonsEnabled = false;
        disabledReason = 'هذا المنتج غير متوفر';
      }
    }

    return Container(
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).scaffoldBackgroundColor,
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).colorScheme.shadow.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: Offset(0, -2),
          ),
        ],
      ),
      child: Row(
        children: [
          // Buy Now Button (Secondary)
          Expanded(
            child: OutlinedButton(
              onPressed: buttonsEnabled ? () async {
                // First add item to cart, then navigate
                await _addItemToCart();
                // Navigate to cart after adding (only if mounted)
                if (mounted) {
                  Navigator.pushNamed(context, '/cart');
                }
              } : () {
                // Show disabled reason
                showToast(
                  title: trans("تنبيه"),
                  description: disabledReason,
                  style: ToastNotificationStyleType.warning,
                );
              },
              style: OutlinedButton.styleFrom(
                side: BorderSide(color: Theme.of(context).colorScheme.primary),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                padding: EdgeInsets.symmetric(vertical: 16),
              ),
              child: Text(
                'اشتر الآن',
                style: TextStyle(
                  color: Theme.of(context).colorScheme.primary,
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),

          SizedBox(width: 12),

          // Add to Cart Button (Primary) - Enhanced with animations
          Expanded(
            child: _AnimatedAddToCartButton(
              onPressed: buttonsEnabled ? _addItemToCart : () {
                // Show disabled reason
                showToast(
                  title: trans("تنبيه"),
                  description: disabledReason,
                  style: ToastNotificationStyleType.warning,
                );
              },
              text: 'أضف إلى السلة',
              enabled: buttonsEnabled,
            ),
          ),
        ],
      ),
    );
  }

  void _showMoreOptions(BuildContext context) {
    showModalBottomSheet(
      context: context,
      builder: (BuildContext context) {
        return Container(
          padding: EdgeInsets.all(16),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              ListTile(
                leading: Icon(Icons.ios_share, color: Theme.of(context).colorScheme.primary),
                title: Text('مشاركة المنتج'),
                onTap: () {
                  Navigator.pop(context);
                  // Implement share functionality
                },
              ),
              ListTile(
                leading: Icon(Icons.flag_outlined, color: Theme.of(context).colorScheme.primary),
                title: Text('الإبلاغ عن مشكلة'),
                onTap: () {
                  Navigator.pop(context);
                  // Implement report functionality
                },
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildDescriptionWidget() {
    String description = _product?.description ?? '';

    if (description.isEmpty) {
      return Text(
        'لا يوجد وصف متاح',
        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.grey[600],
              height: 1.5,
            ),
        textAlign: TextAlign.right,
      );
    }

    // Check if description contains HTML tags
    if (description.contains('<') && description.contains('>')) {
      return HtmlWidget(
        description,
        textStyle: Theme.of(context).textTheme.bodyMedium?.copyWith(
              height: 1.5,
            ),
        customStylesBuilder: (element) {
          if (element.localName == 'p') {
            return {'text-align': 'right', 'direction': 'rtl'};
          }
          return null;
        },
      );
    } else {
      // Plain text description
      return Text(
        description,
        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              height: 1.5,
            ),
        textAlign: TextAlign.right,
      );
    }
  }
}

/// Enhanced Add to Cart Button with sophisticated micro-interactions and success feedback
class _AnimatedAddToCartButton extends StatefulWidget {
  final VoidCallback? onPressed;
  final String text;
  final bool enabled; // PHASE 2.2: Add enabled state

  const _AnimatedAddToCartButton({
    required this.onPressed,
    required this.text,
    this.enabled = true, // Default to enabled
  });

  @override
  State<_AnimatedAddToCartButton> createState() => _AnimatedAddToCartButtonState();
}

class _AnimatedAddToCartButtonState extends State<_AnimatedAddToCartButton>
    with TickerProviderStateMixin {
  late AnimationController _scaleController;
  late AnimationController _successController;
  late AnimationController _pulseController;

  late Animation<double> _scaleAnimation;
  late Animation<double> _successAnimation;
  late Animation<double> _pulseAnimation;

  bool _isProcessing = false;
  bool _showSuccess = false;

  @override
  void initState() {
    super.initState();

    // Scale animation for press feedback
    _scaleController = AnimationController(
      duration: DesignConstants.fastAnimation,
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _scaleController,
      curve: DesignConstants.elegantCurve,
    ));

    // Success animation for add to cart feedback
    _successController = AnimationController(
      duration: DesignConstants.slowAnimation,
      vsync: this,
    );
    _successAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _successController,
      curve: DesignConstants.bouncyCurve,
    ));

    // Pulse animation for attention
    _pulseController = AnimationController(
      duration: Duration(milliseconds: 1000),
      vsync: this,
    );
    _pulseAnimation = Tween<double>(
      begin: 1.0,
      end: 1.1,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _scaleController.dispose();
    _successController.dispose();
    _pulseController.dispose();
    super.dispose();
  }

  void _handleTap() async {
    if (widget.onPressed == null || _isProcessing || !widget.enabled) return;

    setState(() => _isProcessing = true);
    _scaleController.forward();

    // Execute the add to cart action
    widget.onPressed!();

    // Show success animation
    setState(() {
      _showSuccess = true;
      _isProcessing = false;
    });

    _successController.forward();
    _pulseController.repeat(reverse: true);

    // Reset after success animation
    await Future.delayed(Duration(milliseconds: 1500));

    if (mounted) {
      setState(() => _showSuccess = false);
      _successController.reset();
      _pulseController.stop();
      _scaleController.reverse();
    }
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: Listenable.merge([
        _scaleAnimation,
        _successAnimation,
        _pulseAnimation,
      ]),
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value * (_showSuccess ? _pulseAnimation.value : 1.0),
          child: Container(
            height: 56,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(DesignConstants.buttonRadius),
              gradient: _showSuccess
                  ? LinearGradient(
                      colors: [
                        ThemeColor.get(context).successColor,
                        ThemeColor.get(context).successColor.withValues(alpha: 0.8),
                      ],
                    )
                  : widget.enabled
                      ? LinearGradient(
                          colors: [
                            Theme.of(context).colorScheme.primary,
                            Theme.of(context).colorScheme.primary.withValues(alpha: 0.8),
                          ],
                        )
                      : LinearGradient(
                          colors: [
                            ThemeColor.get(context).surfaceContent.withValues(alpha: 0.3),
                            ThemeColor.get(context).surfaceContent.withValues(alpha: 0.5),
                          ],
                        ),
              boxShadow: DesignConstants.buttonShadow.map((shadow) {
                return shadow.copyWith(
                  color: _showSuccess
                      ? ThemeColor.get(context).successColor.withValues(alpha: 0.3)
                      : shadow.color,
                );
              }).toList(),
            ),
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                borderRadius: BorderRadius.circular(DesignConstants.buttonRadius),
                onTap: _handleTap,
                child: Container(
                  padding: EdgeInsets.symmetric(vertical: 16),
                  child: AnimatedSwitcher(
                    duration: DesignConstants.normalAnimation,
                    child: _showSuccess
                        ? Row(
                            key: ValueKey('success'),
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Transform.scale(
                                scale: _successAnimation.value,
                                child: Icon(
                                  Icons.check_circle,
                                  color: Theme.of(context).colorScheme.onPrimary,
                                  size: 20,
                                ),
                              ),
                              SizedBox(width: 8),
                              Text(
                                'تمت الإضافة!',
                                style: TextStyle(
                                  color: Theme.of(context).colorScheme.onPrimary,
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          )
                        : _isProcessing
                            ? SizedBox(
                                key: ValueKey('loading'),
                                height: 20,
                                width: 20,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                  valueColor: AlwaysStoppedAnimation<Color>(Theme.of(context).colorScheme.onPrimary),
                                ),
                              )
                            : Text(
                                key: ValueKey('default'),
                                widget.text,
                                style: TextStyle(
                                  color: Theme.of(context).colorScheme.onPrimary,
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}
